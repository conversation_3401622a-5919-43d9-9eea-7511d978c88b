{"name": "video", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build"}, "dependencies": {"@arco-design/web-vue": "^2.57.0", "nertc-web-sdk": "^5.8.20", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.0", "vite": "^6.3.5", "vite-plugin-vue-devtools": "^7.7.7", "vue-tsc": "^2.2.10"}}